"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_loginHelper = require("../../utils/login-helper.js");
const utils_api = require("../../utils/api.js");
const config_routes = require("../../config/routes.js");
const utils_wechatAuth = require("../../utils/wechat-auth.js");
const utils_userStore = require("../../utils/user-store.js");
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  _easycom_uni_load_more2();
}
const _easycom_uni_load_more = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
const _sfc_main = {
  __name: "login",
  setup(__props) {
    const isLogging = common_vendor.ref(false);
    const isPhoneLogging = common_vendor.ref(false);
    const showPhoneLogin = common_vendor.ref(false);
    const countdown = common_vendor.ref(0);
    const phoneForm = common_vendor.ref({
      phone: "",
      code: ""
    });
    const loadingText = common_vendor.ref({
      contentText: {
        contentdown: "正在登录...",
        contentrefresh: "正在登录...",
        contentnomore: "登录完成"
      }
    });
    const isAgreed = common_vendor.ref(false);
    const toggleAgreement = () => {
      isAgreed.value = !isAgreed.value;
    };
    common_vendor.computed(() => {
      return phoneForm.value.phone.length === 11 && countdown.value === 0;
    });
    common_vendor.computed(() => {
      return phoneForm.value.phone.length === 11 && phoneForm.value.code.length === 6 && !isPhoneLogging.value;
    });
    common_vendor.computed(() => {
      return countdown.value > 0 ? `${countdown.value}s` : "获取验证码";
    });
    common_vendor.onMounted(() => {
      checkLoginStatus();
    });
    async function checkLoginStatus() {
      try {
        const result = await utils_loginHelper.checkAndHandleLoginStatus({
          autoLogin: false,
          // 页面加载时不自动登录
          autoNavigate: true,
          // 自动导航
          showLoading: false
          // 不显示加载提示
        });
        if (result.success && result.isLogin) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:130", "用户已登录，自动跳转");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:133", "检查登录状态失败:", error);
      }
    }
    async function handleWechatLogin() {
      if (isLogging.value)
        return;
      if (!isAgreed.value) {
        common_vendor.index.showToast({
          title: "请先阅读并勾选《用户服务协议》和《个人信息保护政策》",
          icon: "none"
        });
        return;
      }
      isLogging.value = true;
      try {
        const code = await getWechatLoginCode();
        const nickname = await showNicknameInputModal();
        if (!nickname) {
          common_vendor.index.showToast({
            title: "登录已取消",
            icon: "none"
          });
          return;
        }
        const result = await performWechatLoginWithUserInfo({
          code,
          nickname,
          showLoading: false,
          // 我们自己管理加载状态
          autoNavigate: true
          // 自动处理导航
        });
        if (result.success) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:176", "登录成功:", result.type);
        } else {
          utils_loginHelper.showLoginError(result.error);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:181", "登录失败:", error);
        utils_loginHelper.showLoginError(error.message || "登录失败");
      } finally {
        isLogging.value = false;
      }
    }
    function getWechatLoginCode() {
      return new Promise((resolve, reject) => {
        common_vendor.wx$1.login({
          success: (res) => {
            if (res.code) {
              resolve(res.code);
            } else {
              reject(new Error("获取微信登录code失败"));
            }
          },
          fail: (error) => {
            reject(new Error("微信登录失败：" + error.errMsg));
          }
        });
      });
    }
    function showNicknameInputModal() {
      return new Promise((resolve) => {
        var _a;
        const storedInfo = utils_wechatAuth.wechatAuth.getStoredUserInfo();
        ((_a = storedInfo.wechatUserInfo) == null ? void 0 : _a.nickName) || "";
        common_vendor.index.showModal({
          title: "设置昵称",
          content: "请输入您的昵称",
          editable: true,
          placeholderText: "请输入昵称",
          confirmText: "确定",
          cancelText: "取消",
          success: (res) => {
            var _a2;
            if (res.confirm) {
              const nickname = (_a2 = res.content) == null ? void 0 : _a2.trim();
              if (nickname && nickname.length > 0) {
                resolve(nickname);
              } else {
                common_vendor.index.showToast({
                  title: "昵称不能为空",
                  icon: "none"
                });
                setTimeout(() => {
                  showNicknameInputModal().then(resolve);
                }, 1500);
              }
            } else {
              resolve(null);
            }
          },
          fail: () => {
            resolve(null);
          }
        });
      });
    }
    async function performWechatLoginWithUserInfo({ code, nickname, showLoading = true, autoNavigate = true }) {
      var _a;
      try {
        if (showLoading) {
          common_vendor.index.showLoading({
            title: "登录中...",
            mask: true
          });
        }
        const defaultAvatarUrl = "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132";
        const response = await utils_api.api.wechat.login({
          js_code: code,
          nickname,
          avatar_url: defaultAvatarUrl
        });
        if (response && response.success !== false) {
          const userInfo = {
            nickName: nickname,
            avatarUrl: defaultAvatarUrl
          };
          utils_wechatAuth.wechatAuth.handleNicknameInput(nickname);
          utils_userStore.userStore.setWechatUserInfo(userInfo);
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success",
            duration: 2e3
          });
          if (autoNavigate) {
            setTimeout(() => {
              common_vendor.index.switchTab({
                url: "/pages/mine/mine"
              });
            }, 2e3);
          }
          return {
            success: true,
            type: utils_loginHelper.LOGIN_RESULT_TYPE.SUCCESS,
            userInfo: ((_a = response.data) == null ? void 0 : _a.userInfo) || response.userInfo,
            wechatUserInfo: userInfo
          };
        } else {
          throw new Error("登录失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:302", "微信登录失败:", error);
        return {
          success: false,
          error: error.message || "登录失败"
        };
      } finally {
        if (showLoading) {
          common_vendor.index.hideLoading();
        }
      }
    }
    function showPrivacyPolicy() {
      config_routes.navigate.toPrivacyPolicy();
    }
    function showUserAgreement() {
      config_routes.navigate.toUserAgreement();
    }
    function onPhoneLoginShow() {
      common_vendor.index.__f__("log", "at pages/login/login.vue:409", "手机登录功能已移除，请使用微信登录");
    }
    common_vendor.watch(() => showPhoneLogin.value, (newVal) => {
      if (newVal) {
        onPhoneLoginShow();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$1,
        b: common_vendor.t(isLogging.value ? "登录中..." : "微信快速登录"),
        c: isLogging.value,
        d: common_vendor.o(handleWechatLogin),
        e: isAgreed.value,
        f: common_vendor.o(toggleAgreement),
        g: common_vendor.o(showUserAgreement),
        h: common_vendor.o(showPrivacyPolicy),
        i: isLogging.value
      }, isLogging.value ? {
        j: common_vendor.p({
          status: "loading",
          ["content-text"]: loadingText.value
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
