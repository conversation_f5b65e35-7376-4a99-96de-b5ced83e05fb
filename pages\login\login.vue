<template>
  <view class="login-container">
    <!-- 顶部logo区域 -->
    <view class="logo-section">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <view class="app-name">华泰民商调解中心</view>
      <!-- <view class="app-desc">专业的不良资产处置平台</view> -->
    </view>

    <!-- 登录表单区域 -->
    <view class="login-form">
      <!-- 微信登录按钮 -->
      <button 
        class="wechat-login-btn"
        :disabled="isLogging"
        @click="handleWechatLogin"
      >
        <text class="wechat-icon">&#xe65f;</text>
        <text class="login-text">
          {{ isLogging ? '登录中...' : '微信快速登录' }}
        </text>
      </button>

      <!-- <view class="other-login">
        <view class="divider">
          <text class="divider-text">其他登录方式</text>
        </view>
        
        <button 
          class="phone-login-btn"
          @click="showPhoneLogin = true"
        >
          <text class="phone-icon">&#xe60c;</text>
          <text>手机号登录</text>
        </button>
      </view> -->
    </view>

    <!-- 底部协议 -->
    <view class="agreement-section">
      <view class="agreement-text">
         <checkbox-group @change="toggleAgreement">
          <checkbox :checked="isAgreed" color="#2979FF" />
        </checkbox-group>
        我已阅读并同意遵循<text class="link" @click="showUserAgreement">《用户服务协议》</text>和<text class="link" @click="showPrivacyPolicy">《个人信息保护政策》</text>
      </view>
    </view>


    <!-- 加载遮罩 -->
    <view class="loading-mask" v-if="isLogging">
      <view class="loading-content">
        <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import {
  performWechatLogin,
  checkAndHandleLoginStatus,
  showLoginError,
  LOGIN_RESULT_TYPE
} from '@/utils/login-helper.js';
import { api } from '@/utils/api.js';
import { navigate } from '@/config/routes.js';

// 响应式数据
const isLogging = ref(false);
const isPhoneLogging = ref(false);
const showPhoneLogin = ref(false);
const countdown = ref(0);

// 手机登录表单
const phoneForm = ref({
  phone: '',
  code: ''
});

// 加载文案
const loadingText = ref({
  contentText: {
    contentdown: '正在登录...',
    contentrefresh: '正在登录...',
    contentnomore: '登录完成'
  }
});

const isAgreed = ref(false);

const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value;
};

// 计算属性
const canSendCode = computed(() => {
  return phoneForm.value.phone.length === 11 && countdown.value === 0;
});

const canSubmitPhone = computed(() => {
  return phoneForm.value.phone.length === 11 && 
         phoneForm.value.code.length === 6 && 
         !isPhoneLogging.value;
});

const codeButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s` : '获取验证码';
});

// 页面加载
onMounted(() => {
  // 检查是否已经登录
  checkLoginStatus();
});

// 检查登录状态 - 使用新的登录辅助工具
async function checkLoginStatus() {
  try {
    const result = await checkAndHandleLoginStatus({
      autoLogin: false,    // 页面加载时不自动登录
      autoNavigate: true,  // 自动导航
      showLoading: false   // 不显示加载提示
    });

    if (result.success && result.isLogin) {
      // 已登录，自动导航逻辑已在 checkAndHandleLoginStatus 中处理
      console.log('用户已登录，自动跳转');
    }
  } catch (error) {
    console.error('检查登录状态失败:', error);
  }
}

// 微信登录 - 使用新的登录辅助工具
async function handleWechatLogin() {
  if (isLogging.value) return;

  if (!isAgreed.value) {
    uni.showToast({
      title: '请先阅读并勾选《用户服务协议》和《个人信息保护政策》',
      icon: 'none'
    });
    return;
  }

  isLogging.value = true;

  // 使用登录辅助工具执行完整的登录流程
  const result = await performWechatLogin({
    showLoading: false,  // 我们自己管理加载状态
    autoNavigate: true   // 自动处理导航
  });

  if (result.success) {
    // 登录成功，导航逻辑已在 performWechatLogin 中处理
    console.log('登录成功:', result.type);
    
  } else {
    // 登录失败，显示错误信息
    showLoginError(result.error);
  }
  isLogging.value = false;
}

// 发送验证码
/* async function sendVerifyCode() {
  if (!canSendCode.value) return;
  
  try {
    // 调用发送验证码API
    await api.user.sendVerifyCode({
      phone: phoneForm.value.phone
    });
    
    // 开始倒计时
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    });
  } catch (error) {
    uni.showToast({
      title: error.message || '发送验证码失败',
      icon: 'none'
    });
  }
} */

// 手机号登录
/* async function handlePhoneLogin() {
  if (!canSubmitPhone.value) return;
  
  try {
    isPhoneLogging.value = true;
    
    const result = await api.user.phoneLogin({
      phone: phoneForm.value.phone,
      code: phoneForm.value.code
    });
    
    if (result.success) {
      // 保存登录信息
      api.auth.setToken(result.data.token);
      userStore.setUserInfo(result.data.userInfo);
      
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });
      
      closePhoneLogin();
      
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
    }
  } catch (error) {
    uni.showToast({
      title: error.message || '登录失败',
      icon: 'none'
    });
  } finally {
    isPhoneLogging.value = false;
  }
} */

// 关闭手机登录弹窗
function closePhoneLogin() {
  showPhoneLogin.value = false;
  // 重置表单
  phoneForm.value = {
    phone: '',
    code: ''
  };
  countdown.value = 0;
}

// 显示隐私政策
function showPrivacyPolicy() {
  navigate.toPrivacyPolicy();
}

// 显示用户协议
function showUserAgreement() {
  navigate.toUserAgreement();
}

// 监听弹窗显示
function onPhoneLoginShow() {
  // 手机登录弹窗功能已移除，使用微信登录
  console.log('手机登录功能已移除，请使用微信登录');
}

// 监听showPhoneLogin变化
watch(() => showPhoneLogin.value, (newVal) => {
  if (newVal) {
    onPhoneLoginShow();
  }
});
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: transparent;
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #080808;
  margin-bottom: 20rpx;
}

.app-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-form {
  margin-top: 120rpx;
}

.wechat-login-btn {
  width: 100%;
  height: 100rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 40rpx;
  border: none;
}

.wechat-login-btn:disabled {
  background-color: #cccccc;
}

.wechat-icon {
  font-family: 'iconfont';
  font-size: 36rpx;
  margin-right: 20rpx;
}

.other-login {
  margin-top: 60rpx;
}

.divider {
  text-align: center;
  margin-bottom: 40rpx;
}

.divider-text {
  font-size: 24rpx;
  color: #999999;
  background: transparent;
  padding: 0 20rpx;
}

.phone-login-btn {
  width: 100%;
  height: 88rpx;
  background-color: #f8f8f8;
  color: #333333;
  border: 2rpx solid #e5e5e5;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.phone-icon {
  font-family: 'iconfont';
  font-size: 32rpx;
  margin-right: 16rpx;
}

.agreement-section {
  text-align: center;
}

.agreement-text {
  display: flex;
  flex-wrap: wrap;
  font-size: 30rpx;
  color: #989898;
}

.link {
  color: #6E9FEB;
}


.phone-login-popup {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.popup-close {
  font-size: 48rpx;
  color: #999999;
}

.phone-form {
  .input-group {
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }
  
  .phone-input,
  .code-input {
    flex: 1;
    height: 88rpx;
    border: 2rpx solid #e5e5e5;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 30rpx;
  }
  
  .send-code-btn {
    width: 200rpx;
    height: 88rpx;
    background-color: #007aff;
    color: #ffffff;
    border-radius: 8rpx;
    font-size: 26rpx;
    margin-left: 20rpx;
  }
  
  .send-code-btn:disabled {
    background-color: #cccccc;
  }
  
  .phone-submit-btn {
    width: 100%;
    height: 88rpx;
    background-color: #007aff;
    color: #ffffff;
    border-radius: 8rpx;
    font-size: 32rpx;
    margin-top: 20rpx;
  }
  
  .phone-submit-btn:disabled {
    background-color: #cccccc;
  }
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}
</style>
