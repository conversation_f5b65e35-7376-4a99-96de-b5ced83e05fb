"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const utils_wechatAuth = require("./wechat-auth.js");
require("./user-store.js");
const utils_errorHandler = require("./error-handler.js");
const LOGIN_RESULT_TYPE = {
  SUCCESS: "success",
  // 登录成功
  NEED_USER_INFO: "need_user_info",
  // 需要完善用户信息
  FAILED: "failed",
  // 登录失败
  CANCELLED: "cancelled"
  // 用户取消
};
async function checkAndHandleLoginStatus(options = {}) {
  const defaultOptions = {
    autoLogin: false,
    autoNavigate: false,
    showLoading: false,
    ...options
  };
  try {
    if (defaultOptions.showLoading) {
      common_vendor.index.showLoading({
        title: "检查登录状态...",
        mask: true
      });
    }
    const result = await utils_wechatAuth.autoLoginCheck({
      baseURL: config_env.getBaseURL(),
      autoLogin: defaultOptions.autoLogin
    });
    if (result.isLogin) {
      if (defaultOptions.autoNavigate) {
        if (result.needUserInfo) {
          common_vendor.index.navigateTo({
            url: "/pages/user-info/user-info"
          });
        } else {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      }
      return {
        type: result.needUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,
        success: true,
        isLogin: true,
        needUserInfo: result.needUserInfo,
        userData: result.userData
      };
    } else {
      return {
        type: LOGIN_RESULT_TYPE.FAILED,
        success: false,
        isLogin: false,
        needLogin: true,
        reason: result.reason
      };
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/login-helper.js:290", "检查登录状态失败:", error);
    return {
      type: LOGIN_RESULT_TYPE.FAILED,
      success: false,
      error: error.message
    };
  } finally {
    if (defaultOptions.showLoading) {
      common_vendor.index.hideLoading();
    }
  }
}
function showLoginError(error, options = {}) {
  const defaultOptions = {
    duration: 2e3,
    customMessages: {
      [utils_errorHandler.ERROR_TYPES.NETWORK]: {
        message: "网络连接失败，请检查网络设置"
      },
      [utils_errorHandler.ERROR_TYPES.TIMEOUT]: {
        message: "登录超时，请重试"
      },
      [utils_errorHandler.ERROR_TYPES.AUTHORIZATION]: {
        message: "微信授权失败，请重新登录"
      },
      [utils_errorHandler.ERROR_TYPES.USER_CANCELLED]: {
        message: "用户取消登录"
      }
    },
    ...options
  };
  utils_errorHandler.showError(error, defaultOptions);
}
exports.LOGIN_RESULT_TYPE = LOGIN_RESULT_TYPE;
exports.checkAndHandleLoginStatus = checkAndHandleLoginStatus;
exports.showLoginError = showLoginError;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/login-helper.js.map
