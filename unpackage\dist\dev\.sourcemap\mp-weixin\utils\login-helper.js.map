{"version": 3, "file": "login-helper.js", "sources": ["utils/login-helper.js"], "sourcesContent": ["// 微信登录辅助工具 - 2024年增强版本\nimport { getBaseURL } from '@/config/env.js';\nimport wechatAuth, { \n  quickLogin, \n  needUserInfo,\n  getUserInfo,\n  getCompleteUserData,\n  autoLoginCheck,\n  LOGIN_STATUS,\n  USER_INFO_STATUS\n} from '@/utils/wechat-auth.js';\nimport userStore from '@/utils/user-store.js';  // 修复：使用默认导入\nimport { withErrorBoundary, ERROR_TYPES, showError } from '@/utils/error-handler.js';\nimport { withWechatErrorHandling, showWechatError } from '@/utils/wechat-error-handler.js';\n\n/**\n * 登录结果类型枚举\n */\nexport const LOGIN_RESULT_TYPE = {\n  SUCCESS: 'success',          // 登录成功\n  NEED_USER_INFO: 'need_user_info', // 需要完善用户信息\n  FAILED: 'failed',           // 登录失败\n  CANCELLED: 'cancelled'      // 用户取消\n};\n\n/**\n * 执行完整的微信登录流程 - 增强版本，支持用户信息授权\n * @param {Object} options 登录选项\n * @returns {Promise<Object>} 登录结果\n */\nexport async function performWechatLogin(options = {}) {\n  const defaultOptions = {\n    baseURL: getBaseURL(),\n    timeout: 15000, // 增加超时时间以支持用户授权流程\n    showLoading: true,\n    autoNavigate: true,\n    enableRetry: true,\n    maxRetries: 2,\n    showAuthTip: true, // 是否显示用户授权说明\n    ...options\n  };\n\n  const loginOperation = async () => {\n    if (defaultOptions.showLoading) {\n      uni.showLoading({\n        title: '登录中...',\n        mask: true\n      });\n    }\n\n    try {\n      // 执行优化后的微信登录流程（包含用户信息授权）\n      const loginResult = await wechatAuth.login({\n        baseURL: defaultOptions.baseURL,\n        timeout: defaultOptions.timeout,\n        showAuthTip: defaultOptions.showAuthTip\n      });\n\n      if (loginResult.success) {\n        // 更新用户状态管理\n        userStore.setUserInfo(loginResult.userInfo);\n\n        // 获取完整用户数据\n        const completeUserData = wechatAuth.getCompleteUserData();\n\n        // 2024年更新：由于移除了废弃的getUserProfile，所有新用户都需要完善信息\n        // 检查本地是否已有通过头像昵称填写能力保存的用户信息\n        const storedWechatInfo = wechatAuth.getStoredUserInfo().wechatUserInfo;\n        const hasCompleteWechatInfo = storedWechatInfo && storedWechatInfo.nickName && storedWechatInfo.avatarUrl;\n        const needsUserInfo = !hasCompleteWechatInfo;\n\n        const result = {\n          type: needsUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,\n          success: true,\n          userInfo: loginResult.userInfo,\n          wechatUserInfo: storedWechatInfo, // 使用本地存储的微信用户信息\n          completeUserData,\n          needsUserInfo,\n          openid: loginResult.openid,\n          unionid: loginResult.unionid,\n          hasWechatInfo: hasCompleteWechatInfo // 是否有完整的微信用户信息\n        };\n\n        // 自动导航\n        if (defaultOptions.autoNavigate) {\n          await handleLoginNavigation(result);\n        }\n\n        return result;\n      } else {\n        throw new Error('登录失败');\n      }\n    } finally {\n      if (defaultOptions.showLoading) {\n        uni.hideLoading();\n      }\n    }\n  };\n\n  // 简化错误处理，避免多层包装导致成功时也触发错误处理\n  try {\n    let result;\n    if (defaultOptions.enableRetry) {\n      result = await withWechatErrorHandling(loginOperation, {\n        maxRetries: defaultOptions.maxRetries,\n        showError: false, // 我们稍后统一处理错误显示\n        onRetry: (attempt, error, delay) => {\n          uni.showToast({\n            title: `登录失败，${Math.round(delay/1000)}秒后重试...`,\n            icon: 'none',\n            duration: Math.min(delay, 3000)\n          });\n        }\n      });\n    } else {\n      result = await loginOperation();\n    }\n    \n    return result;\n  } catch (error) {\n    // 使用微信专用的错误处理显示友好提示\n    showWechatError(error);\n\n    return {\n      type: LOGIN_RESULT_TYPE.FAILED,\n      success: false,\n      error: error.message || '登录失败',\n      errorType: ERROR_TYPES.UNKNOWN\n    };\n  }\n\n  return result;\n}\n\n/**\n * 处理登录成功后的导航逻辑 - 增强版本，支持微信用户信息状态判断\n * @param {Object} loginResult 登录结果\n */\nexport async function handleLoginNavigation(loginResult) {\n  try {\n    // 获取当前页面栈\n    const pages = getCurrentPages();\n    const currentPage = pages[pages.length - 1];\n    const currentRoute = currentPage ? currentPage.route : '';\n\n    // 记录登录成功的操作日志\n    try {\n      await recordLoginOperation({\n        action: 'login_success',\n        loginType: 'wechat_miniprogram',\n        hasWechatInfo: loginResult.hasWechatInfo,\n        needsUserInfo: loginResult.needsUserInfo,\n        route: currentRoute\n      });\n    } catch (error) {\n      console.warn('记录登录操作日志失败:', error);\n    }\n\n    // 根据登录结果类型和用户信息完整性进行导航\n    if (loginResult.type === LOGIN_RESULT_TYPE.NEED_USER_INFO) {\n      // 需要完善用户信息，跳转到信息完善页面\n      uni.showToast({\n        title: '请完善头像昵称信息',\n        icon: 'none',\n        duration: 2000\n      });\n\n      // 跳转到用户信息完善页面\n      /* try {\n        uni.navigateTo({\n          url: '/pages/user-info/user-info'\n        });\n        return;\n      } catch (error) {\n        console.error('跳转到用户信息页面失败:', error);\n        // 降级处理：跳转到个人中心\n        uni.switchTab({\n          url: '/pages/mine/mine'\n        });\n        return;\n      } */\n    }\n\n    // 默认跳转逻辑\n    if (currentRoute === 'pages/login/login') {\n      // 从登录页进入，跳转到个人中心页面\n      uni.switchTab({\n        url: '/pages/mine/mine'\n      });\n    } else {\n      // 其他页面自动登录成功，显示提示并保持当前页面\n      uni.showToast({\n        title: '登录成功',\n        icon: 'success',\n        duration: 2000\n      });\n    }\n  } catch (error) {\n    console.error('登录导航处理失败:', error);\n    \n    // 降级处理：默认跳转到首页\n    uni.switchTab({\n      url: '/pages/index/index'\n    });\n  }\n}\n\n/**\n * 记录登录相关的操作日志\n * @param {Object} logData 日志数据\n */\nasync function recordLoginOperation(logData) {\n  try {\n    const { api } = require('@/utils/api.js');\n    \n    await api.operationLog.recordOperation({\n      menu_name: '用户登录',\n      button_name: logData.action,\n      browser_path: `/${logData.route}`,\n      operation_details: JSON.stringify({\n        loginType: logData.loginType,\n        hasWechatInfo: logData.hasWechatInfo,\n        needsUserInfo: logData.needsUserInfo,\n        timestamp: new Date().toISOString()\n      })\n    });\n  } catch (error) {\n    console.warn('记录操作日志失败:', error);\n  }\n}\n\n/**\n * 检查登录状态并自动处理\n * @param {Object} options 选项\n * @returns {Promise<Object>} 检查结果\n */\nexport async function checkAndHandleLoginStatus(options = {}) {\n  const defaultOptions = {\n    autoLogin: false,\n    autoNavigate: false,\n    showLoading: false,\n    ...options\n  };\n\n  try {\n    if (defaultOptions.showLoading) {\n      uni.showLoading({\n        title: '检查登录状态...',\n        mask: true\n      });\n    }\n\n    const result = await autoLoginCheck({\n      baseURL: getBaseURL(),\n      autoLogin: defaultOptions.autoLogin\n    });\n\n    if (result.isLogin) {\n      // 已登录\n      if (defaultOptions.autoNavigate) {\n        if (result.needUserInfo) {\n          uni.navigateTo({\n            url: '/pages/user-info/user-info'\n          });\n        } else {\n          uni.switchTab({\n            url: '/pages/index/index'\n          });\n        }\n      }\n\n      return {\n        type: result.needUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,\n        success: true,\n        isLogin: true,\n        needUserInfo: result.needUserInfo,\n        userData: result.userData\n      };\n    } else {\n      // 未登录\n      return {\n        type: LOGIN_RESULT_TYPE.FAILED,\n        success: false,\n        isLogin: false,\n        needLogin: true,\n        reason: result.reason\n      };\n    }\n  } catch (error) {\n    console.error('检查登录状态失败:', error);\n    return {\n      type: LOGIN_RESULT_TYPE.FAILED,\n      success: false,\n      error: error.message\n    };\n  } finally {\n    if (defaultOptions.showLoading) {\n      uni.hideLoading();\n    }\n  }\n}\n\n/**\n * 退出登录\n * @param {Object} options 选项\n */\nexport async function performLogout(options = {}) {\n  const defaultOptions = {\n    showConfirm: true,\n    autoNavigate: true,\n    ...options\n  };\n\n  try {\n    // 显示确认对话框\n    if (defaultOptions.showConfirm) {\n      const confirmResult = await new Promise((resolve) => {\n        uni.showModal({\n          title: '确认退出',\n          content: '确定要退出登录吗？',\n          confirmText: '退出',\n          cancelText: '取消',\n          success: (res) => resolve(res.confirm),\n          fail: () => resolve(false)\n        });\n      });\n\n      if (!confirmResult) {\n        return { success: false, cancelled: true };\n      }\n    }\n\n    // 清除登录信息\n    wechatAuth.clearLoginInfo();\n    userStore.clearUserInfo();\n\n    // 显示成功提示\n    uni.showToast({\n      title: '已退出登录',\n      icon: 'success',\n      duration: 1500\n    });\n\n    // 自动导航到登录页\n    if (defaultOptions.autoNavigate) {\n      setTimeout(() => {\n        uni.reLaunch({\n          url: '/pages/login/login'\n        });\n      }, 1500);\n    }\n\n    return { success: true };\n  } catch (error) {\n    console.error('退出登录失败:', error);\n    uni.showToast({\n      title: '退出失败',\n      icon: 'none'\n    });\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * 获取错误类型\n * @param {string} errorMessage 错误消息\n * @returns {string} 错误类型\n */\nfunction getErrorType(errorMessage) {\n  if (!errorMessage) return 'unknown';\n  \n  const message = errorMessage.toLowerCase();\n  \n  if (message.includes('网络') || message.includes('network')) {\n    return 'network';\n  } else if (message.includes('超时') || message.includes('timeout')) {\n    return 'timeout';\n  } else if (message.includes('取消') || message.includes('cancel')) {\n    return 'cancelled';\n  } else if (message.includes('授权') || message.includes('auth')) {\n    return 'authorization';\n  } else {\n    return 'unknown';\n  }\n}\n\n/**\n * 获取用户友好的错误消息\n * @param {string} errorMessage 原始错误消息\n * @returns {string} 用户友好的错误消息\n */\nexport function getFriendlyErrorMessage(errorMessage) {\n  const errorType = getErrorType(errorMessage);\n  \n  switch (errorType) {\n    case 'network':\n      return '网络连接失败，请检查网络设置';\n    case 'timeout':\n      return '请求超时，请重试';\n    case 'cancelled':\n      return '用户取消操作';\n    case 'authorization':\n      return '授权失败，请重新授权';\n    default:\n      return errorMessage || '操作失败，请重试';\n  }\n}\n\n/**\n * 显示登录错误提示 - 使用增强的错误处理\n * @param {string|Error} error 错误信息\n * @param {Object} options 选项\n */\nexport function showLoginError(error, options = {}) {\n  const defaultOptions = {\n    duration: 2000,\n    customMessages: {\n      [ERROR_TYPES.NETWORK]: {\n        message: '网络连接失败，请检查网络设置'\n      },\n      [ERROR_TYPES.TIMEOUT]: {\n        message: '登录超时，请重试'\n      },\n      [ERROR_TYPES.AUTHORIZATION]: {\n        message: '微信授权失败，请重新登录'\n      },\n      [ERROR_TYPES.USER_CANCELLED]: {\n        message: '用户取消登录'\n      }\n    },\n    ...options\n  };\n\n  showError(error, defaultOptions);\n}\n\n// 导出状态枚举\nexport { LOGIN_STATUS, USER_INFO_STATUS };\n"], "names": ["uni", "autoLoginCheck", "getBaseURL", "ERROR_TYPES", "showError"], "mappings": ";;;;;;AAkBY,MAAC,oBAAoB;AAAA,EAC/B,SAAS;AAAA;AAAA,EACT,gBAAgB;AAAA;AAAA,EAChB,QAAQ;AAAA;AAAA,EACR,WAAW;AAAA;AACb;AAqNO,eAAe,0BAA0B,UAAU,IAAI;AAC5D,QAAM,iBAAiB;AAAA,IACrB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,aAAa;AAAA,IACb,GAAG;AAAA,EACP;AAEE,MAAI;AACF,QAAI,eAAe,aAAa;AAC9BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAED,UAAM,SAAS,MAAMC,gCAAe;AAAA,MAClC,SAASC,WAAAA,WAAY;AAAA,MACrB,WAAW,eAAe;AAAA,IAChC,CAAK;AAED,QAAI,OAAO,SAAS;AAElB,UAAI,eAAe,cAAc;AAC/B,YAAI,OAAO,cAAc;AACvBF,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACjB,CAAW;AAAA,QACX,OAAe;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACjB,CAAW;AAAA,QACF;AAAA,MACF;AAED,aAAO;AAAA,QACL,MAAM,OAAO,eAAe,kBAAkB,iBAAiB,kBAAkB;AAAA,QACjF,SAAS;AAAA,QACT,SAAS;AAAA,QACT,cAAc,OAAO;AAAA,QACrB,UAAU,OAAO;AAAA,MACzB;AAAA,IACA,OAAW;AAEL,aAAO;AAAA,QACL,MAAM,kBAAkB;AAAA,QACxB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,WAAW;AAAA,QACX,QAAQ,OAAO;AAAA,MACvB;AAAA,IACK;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChC,WAAO;AAAA,MACL,MAAM,kBAAkB;AAAA,MACxB,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACnB;AAAA,EACA,UAAY;AACR,QAAI,eAAe,aAAa;AAC9BA,oBAAG,MAAC,YAAW;AAAA,IAChB;AAAA,EACF;AACH;AAiHO,SAAS,eAAe,OAAO,UAAU,IAAI;AAClD,QAAM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,gBAAgB;AAAA,MACd,CAACG,mBAAAA,YAAY,OAAO,GAAG;AAAA,QACrB,SAAS;AAAA,MACV;AAAA,MACD,CAACA,mBAAAA,YAAY,OAAO,GAAG;AAAA,QACrB,SAAS;AAAA,MACV;AAAA,MACD,CAACA,mBAAAA,YAAY,aAAa,GAAG;AAAA,QAC3B,SAAS;AAAA,MACV;AAAA,MACD,CAACA,mBAAAA,YAAY,cAAc,GAAG;AAAA,QAC5B,SAAS;AAAA,MACV;AAAA,IACF;AAAA,IACD,GAAG;AAAA,EACP;AAEEC,+BAAU,OAAO,cAAc;AACjC;;;;"}