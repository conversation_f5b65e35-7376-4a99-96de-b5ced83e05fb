// HTTP请求拦截器模块
import { getBaseURL, getTimeout, isDebug } from '@/config/env.js';

// 全局token刷新状态管理
let isRefreshing = false;
let refreshPromise = null;
const waitingRequests = [];

// 内存缓存优化
let tokenCacheTimer = null;
let lastTokenCheck = 0;
const TOKEN_CHECK_INTERVAL = 5000; // 5秒内最多检查一次token

/**
 * 获取token的统一函数
 * 支持从本地存储和URL参数获取token，优先使用access_token
 */
export const getToken = () => {
  // 优先从本地存储获取access_token
  let token = uni.getStorageSync('access_token');

  // 如果没有access_token，尝试获取旧的token字段（向后兼容）
  if (!token) {
    token = uni.getStorageSync('token');
  }

  // 如果本地存储都没有，尝试从URL参数获取
  if (!token) {
    try {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        token = currentPage.options?.token || '';

        // 如果从URL获取到token，保存到本地存储
        if (token) {
          uni.setStorageSync('access_token', token);
          console.log('从URL参数获取token并保存到本地存储');
        }
      }
    } catch (error) {
      console.log('从URL获取token失败:', error);
    }
  }

  return token;
};

/**
 * 获取token类型
 * 默认返回Bearer
 */
export const getTokenType = () => {
  return uni.getStorageSync('token_type') || 'Bearer';
};

/**
 * 不需要Authorization头部的接口列表
 * 这些接口会跳过自动添加Authorization头部
 */
const NO_AUTH_APIS = [
  '/user/login',                    // 普通登录
  '/user/wechat/login/',           // 微信登录
  '/user/phone-login',             // 手机号登录
  '/user/send-code',               // 发送验证码
  '/user/wechat/refresh/',         // Token刷新（使用refresh_token）
  '/home/<USER>',               // 首页数据（可能不需要认证）
  '/case_display/case_display/',   // 案例展示（可能不需要认证）
];

/**
 * 检查接口是否需要跳过Authorization头部
 */
const shouldSkipAuth = (url) => {
  return NO_AUTH_APIS.some(pattern => {
    // 支持精确匹配和前缀匹配
    return url === pattern || url.startsWith(pattern);
  });
};

/**
 * 请求拦截器 - 增强版本
 * 自动为请求添加Authorization头，支持跳过指定接口
 */
export const requestInterceptor = (config) => {
  // 检查是否需要跳过Authorization
  const skipAuth = config.skipAuth || shouldSkipAuth(config.url);
  
  if (skipAuth) {
    console.log(`接口 ${config.url} 跳过Authorization头部`);
    return config;
  }

  // 获取token和token类型
  const token = getToken();
  const tokenType = getTokenType();

  if (token) {
    config.header = {
      ...config.header,
      'Authorization': `${tokenType} ${token}`
    };
    console.log(`已为请求添加Authorization头: ${tokenType} ${token.substring(0, 10)}...`);
  } else {
    console.warn(`接口 ${config.url} 未找到token，请求将不包含Authorization头`);
  }

  return config;
};

/**
 * 响应拦截器
 * 统一处理响应状态和错误，支持token刷新
 */
export const responseInterceptor = (response) => {
  // 处理401未授权状态
  if (response.statusCode === 401) {
    // token过期或未登录
    uni.showToast({
      title: '登录状态已过期，请重新登录',
      icon: 'none'
    });

    // 清除所有登录相关状态
    const keysToRemove = [
      'token', 'access_token', 'token_type', 'token_expire_time', 'refresh_token',
      'userInfo', 'openid', 'unionid', 'sessionKey', 'wechat_userInfo'
    ];
    keysToRemove.forEach(key => {
      uni.removeStorageSync(key);
    });

    // 跳转到登录页
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/login'
      });
    }, 1500);

    return Promise.reject(new Error('登录状态已过期，请重新登录'));
  }

  // 处理其他错误状态
  if (response.statusCode !== 200) {
    uni.showToast({
      title: response.data.message || '请求失败',
      icon: 'none'
    });
    return Promise.reject(new Error(response.data.message || '请求失败'));
  }

  return response.data;
};

/**
 * 创建带有特定配置的请求
 * @param {Object} options 请求配置
 * @param {boolean} options.skipAuth 是否跳过Authorization头部
 * @returns {Function} 请求函数
 */
export const createRequest = (defaultOptions = {}) => {
  return (options) => {
    return request({
      ...defaultOptions,
      ...options
    });
  };
};

/**
 * 封装的请求方法
 * 集成请求和响应拦截器，支持自动token刷新
 */
export const request = (options) => {
  return new Promise(async (resolve, reject) => {
    try {
      // 1. 检查token有效性（带缓存优化）
      const isTokenValid = authUtils.checkTokenValidityWithCache();
      
      // 2. 如果token无效或即将过期，尝试刷新
      if (!isTokenValid && authUtils.getRefreshToken()) {
        if (isRefreshing) {
          // 如果正在刷新，将请求加入等待队列
          authUtils.addRequestToQueue(options, resolve, reject);
          return;
        }

        try {
          // 执行token刷新
          await authUtils.refreshToken();
          // 刷新成功后处理等待队列
          authUtils.processWaitingRequests();
        } catch (refreshError) {
          // 刷新失败，处理等待队列并返回错误
          authUtils.processWaitingRequests(refreshError);
          reject(refreshError);
          return;
        }
      }

      // 3. 应用请求拦截器（添加Authorization头等）
      const config = requestInterceptor(options);
      
      // 4. 发送请求
      uni.request({
        ...config,
        url: `${getBaseURL()}${config.url}`,
        timeout: getTimeout(),
        success: (res) => {
          try {
            // 5. 应用响应拦截器
            const response = responseInterceptor(res);
            resolve(response);
          } catch (error) {
            // 6. 如果响应拦截器中遇到401错误，且还没有尝试过刷新，则尝试刷新token
            if (res.statusCode === 401 && !isRefreshing && authUtils.getRefreshToken()) {
              // 将当前请求加入队列，然后刷新token
              authUtils.addRequestToQueue(options, resolve, reject);
              
              authUtils.refreshToken()
                .then(() => {
                  // 刷新成功，处理等待队列
                  authUtils.processWaitingRequests();
                })
                .catch((refreshError) => {
                  // 刷新失败，处理等待队列
                  authUtils.processWaitingRequests(refreshError);
                });
            } else {
              reject(error);
            }
          }
        },
        fail: (err) => {
          // 7. 网络错误处理
          let errorMessage = '网络请求失败';
          
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请检查网络连接';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败，请检查网络设置';
            } else {
              errorMessage = `网络请求失败：${err.errMsg}`;
            }
          }

          // 显示错误提示
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });

          reject(new Error(errorMessage));
        }
      });

    } catch (error) {
      // 8. 其他异常处理
      console.error('请求处理异常:', error);
      reject(error);
    }
  });
};

/**
 * Token管理工具 - 增强版本，支持access_token和token_type
 */
export const authUtils = {
  // 设置完整的token信息
  setTokenInfo: (tokenInfo) => {
    const { access_token, token_type = 'Bearer', expires_in } = tokenInfo;

    if (access_token) {
      uni.setStorageSync('access_token', access_token);
      uni.setStorageSync('token_type', token_type);

      // 设置过期时间
      if (expires_in) {
        const expireTime = Date.now() + (expires_in * 1000);
        uni.setStorageSync('token_expire_time', expireTime);
      }

      console.log(`Token信息已保存: ${token_type} ${access_token.substring(0, 10)}...`);
      return true;
    }
    return false;
  },

  // 手动设置token到本地存储（向后兼容）
  setToken: (token) => {
    if (token) {
      uni.setStorageSync('access_token', token);
      uni.setStorageSync('token_type', 'Bearer');
      console.log('Token已保存到本地存储');
      return true;
    }
    return false;
  },

  // 获取当前token
  getToken: () => {
    return getToken();
  },

  // 获取token类型
  getTokenType: () => {
    return getTokenType();
  },

  // 获取完整的Authorization头
  getAuthorizationHeader: () => {
    const token = getToken();
    const tokenType = getTokenType();
    return token ? `${tokenType} ${token}` : null;
  },

  // 检查token是否过期（增强版本，支持提前刷新和容错处理）
  isTokenExpired: (advanceMinutes = 5) => {
    const expireTime = uni.getStorageSync('token_expire_time');
    // 容错机制：如果过期时间不存在或无效，视为过期
    if (!expireTime || expireTime <= 0) return true;
    
    const currentTime = Date.now();
    const advanceTime = advanceMinutes * 60 * 1000; // 提前N分钟刷新，默认5分钟
    
    return currentTime >= (expireTime - advanceTime);
  },

  // 清除token
  clearToken: () => {
    const keysToRemove = [
      'token', 'access_token', 'token_type', 'token_expire_time', 'refresh_token',
      'userInfo', 'openid', 'unionid', 'sessionKey', 'wechat_userInfo'
    ];
    keysToRemove.forEach(key => {
      uni.removeStorageSync(key);
    });
    console.log('所有Token信息已清除');
  },

  // 检查token是否存在且未过期
  hasValidToken: () => {
    const token = getToken();
    return !!token && !authUtils.isTokenExpired();
  },

  // 检查token是否存在（不检查过期）
  hasToken: () => {
    const token = getToken();
    return !!token;
  },

  // 获取refresh_token
  getRefreshToken: () => {
    return uni.getStorageSync('refresh_token') || null;
  },

  // 设置refresh_token
  setRefreshToken: (refreshToken) => {
    if (refreshToken) {
      uni.setStorageSync('refresh_token', refreshToken);
      console.log('Refresh Token已保存');
      return true;
    }
    return false;
  },

  // Token自动刷新核心函数 - 增强版本
  refreshToken: async () => {
    // 防止并发刷新
    if (isRefreshing) {
      return refreshPromise;
    }

    const refreshToken = authUtils.getRefreshToken();
    if (!refreshToken) {
      throw new Error('Refresh Token不存在，请重新登录');
    }

    isRefreshing = true;
    refreshPromise = new Promise(async (resolve, reject) => {
      try {
        console.log('开始刷新Token...');
        
        // 调用刷新API，最多重试2次
        let retryCount = 0;
        const maxRetries = 2;
        
        while (retryCount <= maxRetries) {
          try {
            // 注意：这里不能使用api.wechat.refresh，因为会造成循环依赖
            // Token刷新必须使用原生uni.request避免拦截器循环
            const response = await uni.request({
              url: `${getBaseURL()}/user/wechat/refresh/`,
              method: 'POST',
              header: {
                'Authorization': `Bearer ${refreshToken}`,
                'Content-Type': 'application/json'
              },
              timeout: getTimeout(),
              data: {
                refresh_token: refreshToken
              }
            });

            // 检查响应状态
            if (response.statusCode === 200 && response.data) {
              // 支持多种响应格式
              const responseData = response.data.data || response.data;
              const { access_token, token_type, expires_in, refresh_token: newRefreshToken } = responseData;
              
              if (access_token) {
                // 更新token信息
                authUtils.setTokenInfo({
                  access_token,
                  token_type: token_type || 'Bearer',
                  expires_in
                });

                // 如果返回了新的refresh_token，则更新
                if (newRefreshToken) {
                  authUtils.setRefreshToken(newRefreshToken);
                }

                console.log('Token刷新成功');
                resolve({
                  access_token,
                  token_type: token_type || 'Bearer',
                  expires_in,
                  refresh_token: newRefreshToken || refreshToken
                });
                return;
              }
            }

            // 如果是401/403，说明refresh_token无效，不需要重试
            if (response.statusCode === 401 || response.statusCode === 403) {
              throw new Error('Refresh Token已过期，请重新登录');
            }

            // 其他错误，准备重试
            throw new Error(`刷新失败，状态码: ${response.statusCode}, 响应: ${JSON.stringify(response.data)}`);

          } catch (error) {
            retryCount++;
            
            if (retryCount > maxRetries) {
              throw error;
            }

            // 重试前等待1秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log(`Token刷新重试 ${retryCount}/${maxRetries}`);
          }
        }

      } catch (error) {
        console.error('Token刷新失败:', error.message);
        
        // 刷新失败，清除所有token相关数据
        authUtils.clearToken();
        
        // 显示错误提示（防止重复提示）
        if (!authUtils._showingLogoutToast) {
          authUtils._showingLogoutToast = true;
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none',
            duration: 2000
          });

          // 重置提示状态
          setTimeout(() => {
            authUtils._showingLogoutToast = false;
          }, 3000);
        }

        // 跳转到登录页（防止重复跳转）
        if (!authUtils._navigatingToLogin) {
          authUtils._navigatingToLogin = true;
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login',
              complete: () => {
                authUtils._navigatingToLogin = false;
              }
            });
          }, 2000);
        }

        reject(error);
      } finally {
        isRefreshing = false;
        refreshPromise = null;
      }
    });

    return refreshPromise;
  },

  // 添加请求到等待队列
  addRequestToQueue: (config, resolve, reject) => {
    waitingRequests.push({ config, resolve, reject });
  },

  // 处理等待队列中的请求
  processWaitingRequests: (error = null) => {
    waitingRequests.forEach(({ config, resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        // 重新发起请求
        request(config).then(resolve).catch(reject);
      }
    });
    // 清空队列
    waitingRequests.length = 0;
  },

  // 优化的token有效性检查（带缓存）
  checkTokenValidityWithCache: () => {
    const now = Date.now();
    
    // 防抖：5秒内最多检查一次
    if (now - lastTokenCheck < TOKEN_CHECK_INTERVAL) {
      return true; // 假设在短时间内token状态没有变化
    }
    
    lastTokenCheck = now;
    const hasToken = authUtils.hasToken();
    const isExpired = authUtils.isTokenExpired();
    
    return hasToken && !isExpired;
  }
};
